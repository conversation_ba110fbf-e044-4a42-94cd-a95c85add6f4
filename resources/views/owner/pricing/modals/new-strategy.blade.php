<!-- New Strategy Modal -->
<div class="modal fade" id="newStrategyModal" tabindex="-1" role="dialog" aria-labelledby="newStrategyModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="newStrategyModalLabel">
                    <i class="fas fa-plus-circle mr-2"></i>Create New Pricing Strategy
                </h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="newStrategyForm" action="{{ route('owner.pricing.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="strategy_name">Strategy Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="strategy_name" name="strategy_name"
                                       placeholder="e.g., Summer Peak Pricing" required>
                                <small class="form-text text-muted">Give your strategy a descriptive name</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="strategy_type">Strategy Type <span class="text-danger">*</span></label>
                                <select class="form-control" id="strategy_type" name="strategy_type" required>
                                    <option value="">Select Strategy Type</option>
                                    <option value="fixed">Fixed Pricing</option>
                                    <option value="dynamic">Dynamic Pricing</option>
                                    <option value="seasonal">Seasonal Pricing</option>
                                    <option value="demand_based">Demand-Based Pricing</option>
                                    <option value="time_based">Time-Based Pricing</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="service_id">Apply to Service</label>
                                <select class="form-control" id="service_id" name="service_id">
                                    <option value="">All Services</option>
                                    @if(isset($services))
                                        @foreach($services as $service)
                                            <option value="{{ $service->id }}">{{ $service->name }} (${{ number_format($service->price, 2) }})</option>
                                        @endforeach
                                    @endif
                                </select>
                                <small class="form-text text-muted">Leave blank to apply to all services</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="priority">Priority</label>
                                <select class="form-control" id="priority" name="priority">
                                    <option value="1">Low</option>
                                    <option value="2" selected>Medium</option>
                                    <option value="3">High</option>
                                    <option value="4">Critical</option>
                                </select>
                                <small class="form-text text-muted">Higher priority strategies override lower ones</small>
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Configuration (Dynamic based on strategy type) -->
                    <div id="pricingConfiguration">
                        <!-- Fixed Pricing -->
                        <div id="fixedPricing" class="pricing-config d-none">
                            <h5 class="mt-3 mb-3">Fixed Pricing Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="fixed_price">Fixed Price <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="fixed_price" name="fixed_price" placeholder="50.00">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="fixed_description">Description</label>
                                        <input type="text" class="form-control" id="fixed_description" name="description" placeholder="Standard pricing">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dynamic Pricing -->
                        <div id="dynamicPricing" class="pricing-config d-none">
                            <h5 class="mt-3 mb-3">Dynamic Pricing Configuration</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="base_price">Base Price <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="base_price" name="base_price" placeholder="50.00">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="minimum_price">Minimum Price</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="minimum_price" name="minimum_price" placeholder="30.00">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="maximum_price">Maximum Price</label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="maximum_price" name="maximum_price" placeholder="100.00">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="demand_multiplier">Demand Multiplier</label>
                                        <input type="number" step="0.01" class="form-control" id="demand_multiplier" name="demand_multiplier" placeholder="1.5" value="1.2">
                                        <small class="form-text text-muted">How much to increase price during high demand</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="low_demand_discount">Low Demand Discount (%)</label>
                                        <input type="number" step="1" class="form-control" id="low_demand_discount" name="low_demand_discount" placeholder="15" min="0" max="50">
                                        <small class="form-text text-muted">Discount percentage during low demand</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Seasonal Pricing -->
                        <div id="seasonalPricing" class="pricing-config d-none">
                            <h5 class="mt-3 mb-3">Seasonal Pricing Configuration</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="season_start">Season Start Date</label>
                                        <input type="date" class="form-control" id="season_start" name="season_start">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="season_end">Season End Date</label>
                                        <input type="date" class="form-control" id="season_end" name="season_end">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="seasonal_base_price">Base Price <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <div class="input-group-prepend">
                                                <span class="input-group-text">$</span>
                                            </div>
                                            <input type="number" step="0.01" class="form-control" id="seasonal_base_price" name="seasonal_base_price" placeholder="50.00">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="seasonal_multiplier">Seasonal Multiplier</label>
                                        <input type="number" step="0.01" class="form-control" id="seasonal_multiplier" name="seasonal_multiplier" placeholder="1.3" value="1.2">
                                        <small class="form-text text-muted">Price multiplier for this season</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="repeat_yearly">Repeat Yearly</label>
                                        <select class="form-control" id="repeat_yearly" name="repeat_yearly">
                                            <option value="1">Yes</option>
                                            <option value="0">No</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Strategy Settings -->
                    <div class="mt-4">
                        <h5>Strategy Settings</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" checked>
                                        <label class="custom-control-label" for="is_active">Activate Strategy Immediately</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="auto_adjust" name="auto_adjust">
                                        <label class="custom-control-label" for="auto_adjust">Enable Auto-Adjustment</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" placeholder="Add any additional notes about this pricing strategy..."></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>Create Strategy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Strategy type change handler
    $('#strategy_type').on('change', function() {
        var strategyType = $(this).val();

        // Hide all pricing configs
        $('.pricing-config').addClass('d-none');

        // Show relevant pricing config
        if (strategyType === 'fixed') {
            $('#fixedPricing').removeClass('d-none');
        } else if (strategyType === 'dynamic' || strategyType === 'demand_based') {
            $('#dynamicPricing').removeClass('d-none');
        } else if (strategyType === 'seasonal') {
            $('#seasonalPricing').removeClass('d-none');
        }
    });

    // Form validation and submission
    $('#newStrategyForm').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.html();

        // Show loading state
        submitBtn.html('<i class="fas fa-spinner fa-spin mr-1"></i>Creating...').prop('disabled', true);

        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success(response.message || 'Pricing strategy created successfully!');

                    // Close modal and reset form
                    $('#newStrategyModal').modal('hide');
                    form[0].reset();
                    $('.pricing-config').addClass('d-none');

                    // Reload page or update table
                    if (response.redirect) {
                        window.location.href = response.redirect;
                    } else {
                        location.reload();
                    }
                } else {
                    toastr.error(response.message || 'Error creating pricing strategy');
                }
            },
            error: function(xhr) {
                var errors = xhr.responseJSON?.errors;
                if (errors) {
                    Object.keys(errors).forEach(function(key) {
                        toastr.error(errors[key][0]);
                    });
                } else {
                    toastr.error('Error creating pricing strategy. Please try again.');
                }
            },
            complete: function() {
                // Reset button state
                submitBtn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;
use App\Models\TwoFactorSetting;
use Symfony\Component\HttpFoundation\Response;

class TwoFactorAuthMiddleware
{
    /**
     * Handle an incoming request for critical role operations.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string|null  $actions
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, $actions = null): Response
    {
        $user = Auth::user();

        if (!$user) {
            return redirect()->route('login');
        }

        // Determine the action based on the route and method
        $action = $this->determineAction($request, $actions);

        // Check if 2FA is required for this action
        if ($this->requires2FA($user, $action, $request)) {
            // Check if 2FA has been verified for this session
            $sessionKey = "2fa_verified_{$action}_" . $user->id;

            $sessionDuration = TwoFactorSetting::getSessionDuration();
            if (!Session::has($sessionKey) || Session::get($sessionKey) < now()->subMinutes($sessionDuration)) {
                // Store the intended action and redirect to 2FA verification
                Session::put('2fa_intended_url', $request->fullUrl());
                Session::put('2fa_intended_action', $action);

                Log::info('2FA required for critical action', [
                    'user_id' => $user->id,
                    'action' => $action,
                    'route' => $request->route()->getName(),
                    'method' => $request->method(),
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);

                return redirect()->route('admin.2fa.verify', ['action' => $action])
                    ->with('warning', 'Two-factor authentication required for this critical operation.');
            }
        }

        return $next($request);
    }

    /**
     * Determine the action based on the request
     */
    private function determineAction(Request $request, $actions): string
    {
        $route = $request->route();
        $method = $request->method();
        $routeName = $route ? $route->getName() : '';

        // If specific actions are provided in middleware parameter
        if ($actions) {
            $actionList = explode(',', $actions);

            // Map route methods to actions
            if ($method === 'POST' && $routeName === 'admin.roles.store') {
                return 'role_create';
            } elseif ($method === 'PUT' && str_contains($routeName, 'roles.update')) {
                return 'role_update';
            } elseif ($method === 'DELETE' && str_contains($routeName, 'roles.destroy')) {
                return 'role_delete';
            }

            // Return first action from list as fallback
            return $actionList[0];
        }

        // Default action determination based on route
        if (str_contains($routeName, 'roles')) {
            if ($method === 'POST') return 'role_create';
            if ($method === 'PUT' || $method === 'PATCH') return 'role_update';
            if ($method === 'DELETE') return 'role_delete';
        }

        return 'critical_operation';
    }

    /**
     * Determine if 2FA is required for the given action
     */
    private function requires2FA($user, $action, $request): bool
    {
        // Check if 2FA is globally disabled
        if (!TwoFactorSetting::isEnabled()) {
            // Exception: Always require 2FA for Super Admin if configured
            if ($user->hasRole('Super Admin') && TwoFactorSetting::isRequiredForSuperAdmin()) {
                return true;
            }
            return false;
        }

        // Always require 2FA for Super Admin actions if configured
        if ($user->hasRole('Super Admin') && TwoFactorSetting::isRequiredForSuperAdmin()) {
            return true;
        }

        // Check if 2FA is required for role operations
        $roleActions = ['role_create', 'role_update', 'role_delete'];
        if (in_array($action, $roleActions) && TwoFactorSetting::isRequiredForRoles()) {
            return true;
        }

        // Critical actions that always require 2FA when enabled
        $alwaysCriticalActions = [
            'sensitive_permission_assign',
            'system_settings_modify',
            'security_settings_change',
        ];

        // Check if this is an always-critical action
        if (in_array($action, $alwaysCriticalActions)) {
            return true;
        }

        // Check if request involves sensitive permissions
        if ($request->has('permissions')) {
            $sensitivePermissions = [
                'manage system settings',
                'manage server configuration',
                'manage backups',
                'manage audit logs',
                'manage security settings',
                'manage database',
                'manage file system',
            ];

            $requestedPermissions = $request->input('permissions', []);
            if (is_array($requestedPermissions)) {
                $permissionNames = \Spatie\Permission\Models\Permission::whereIn('id', $requestedPermissions)
                    ->pluck('name')
                    ->toArray();

                if (array_intersect($permissionNames, $sensitivePermissions)) {
                    return true;
                }
            }
        }

        // Check if modifying system roles
        if ($request->route('role')) {
            $role = $request->route('role');
            if (method_exists($role, 'isSystemRole') && $role->isSystemRole()) {
                return true;
            }
        }

        return false;
    }

    /**
     * Mark 2FA as verified for the current session
     */
    public static function mark2FAVerified($action, $userId)
    {
        $sessionKey = "2fa_verified_{$action}_{$userId}";
        Session::put($sessionKey, now());

        Log::info('2FA verification completed', [
            'user_id' => $userId,
            'action' => $action,
            'verified_at' => now()->toISOString(),
        ]);
    }

    /**
     * Clear 2FA verification for all actions
     */
    public static function clear2FAVerification($userId)
    {
        $sessionKeys = collect(Session::all())
            ->keys()
            ->filter(function ($key) use ($userId) {
                return str_starts_with($key, "2fa_verified_") && str_ends_with($key, "_{$userId}");
            });

        foreach ($sessionKeys as $key) {
            Session::forget($key);
        }
    }
}

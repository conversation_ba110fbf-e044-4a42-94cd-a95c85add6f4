<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Routing\Controller;
use App\Models\Business;
use App\Models\BusinessOperatingHour;
use Illuminate\Http\Request;

class BusinessOperatingHoursController extends Controller
{
    // Note: Middleware is applied at the route level in routes/web.php

    /**
     * Display operating hours for a business.
     */
    public function index(Business $business)
    {
        $operatingHours = $business->operatingHours()
            ->orderBy('day_of_week')
            ->get()
            ->keyBy('day_of_week');

        // Ensure we have entries for all days of the week
        $daysOfWeek = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday'
        ];

        foreach ($daysOfWeek as $dayNumber => $dayName) {
            if (!$operatingHours->has($dayNumber)) {
                $operatingHours[$dayNumber] = new BusinessOperatingHour([
                    'business_id' => $business->id,
                    'day_of_week' => $dayNumber,
                    'is_closed' => true
                ]);
            }
        }

        $operatingHours = $operatingHours->sortKeys();

        return view('admin.businesses.operating-hours.index', compact('business', 'operatingHours', 'daysOfWeek'));
    }

    /**
     * Update operating hours for a business.
     */
    public function update(Request $request, Business $business)
    {
        $validated = $request->validate([
            'hours' => 'required|array',
            'hours.*.day_of_week' => 'required|integer|min:0|max:6',
            'hours.*.is_closed' => 'boolean',
            'hours.*.open_time' => 'nullable|date_format:H:i',
            'hours.*.close_time' => 'nullable|date_format:H:i',
            'hours.*.break_times' => 'nullable|array',
            'hours.*.break_times.*.start' => 'nullable|date_format:H:i',
            'hours.*.break_times.*.end' => 'nullable|date_format:H:i',
        ]);

        foreach ($validated['hours'] as $hourData) {
            $dayOfWeek = $hourData['day_of_week'];
            $isClosed = $hourData['is_closed'] ?? false;

            // Validate times if not closed
            if (!$isClosed) {
                if (empty($hourData['open_time']) || empty($hourData['close_time'])) {
                    return back()->withErrors([
                        "hours.{$dayOfWeek}" => "Open and close times are required when not closed."
                    ]);
                }

                if ($hourData['open_time'] >= $hourData['close_time']) {
                    return back()->withErrors([
                        "hours.{$dayOfWeek}" => "Close time must be after open time."
                    ]);
                }
            }

            // Process break times
            $breakTimes = [];
            if (!$isClosed && !empty($hourData['break_times'])) {
                foreach ($hourData['break_times'] as $break) {
                    if (!empty($break['start']) && !empty($break['end'])) {
                        if ($break['start'] >= $break['end']) {
                            return back()->withErrors([
                                "hours.{$dayOfWeek}.break_times" => "Break end time must be after start time."
                            ]);
                        }
                        $breakTimes[] = $break;
                    }
                }
            }

            // Update or create operating hours
            BusinessOperatingHour::updateOrCreate(
                [
                    'business_id' => $business->id,
                    'day_of_week' => $dayOfWeek
                ],
                [
                    'is_closed' => $isClosed,
                    'open_time' => $isClosed ? null : $hourData['open_time'],
                    'close_time' => $isClosed ? null : $hourData['close_time'],
                    'break_times' => empty($breakTimes) ? null : $breakTimes
                ]
            );
        }

        return redirect()->route('admin.businesses.operating-hours.index', $business)
                        ->with('success', 'Operating hours updated successfully.');
    }

    /**
     * Copy operating hours from one day to another.
     */
    public function copy(Request $request, Business $business)
    {
        $validated = $request->validate([
            'from_day' => 'required|integer|min:0|max:6',
            'to_days' => 'required|array',
            'to_days.*' => 'integer|min:0|max:6'
        ]);

        $sourceHours = BusinessOperatingHour::where('business_id', $business->id)
            ->where('day_of_week', $validated['from_day'])
            ->first();

        if (!$sourceHours) {
            return back()->withErrors(['from_day' => 'Source day operating hours not found.']);
        }

        foreach ($validated['to_days'] as $targetDay) {
            if ($targetDay == $validated['from_day']) {
                continue; // Skip copying to the same day
            }

            BusinessOperatingHour::updateOrCreate(
                [
                    'business_id' => $business->id,
                    'day_of_week' => $targetDay
                ],
                [
                    'is_closed' => $sourceHours->is_closed,
                    'open_time' => $sourceHours->open_time,
                    'close_time' => $sourceHours->close_time,
                    'break_times' => $sourceHours->break_times
                ]
            );
        }

        return redirect()->route('admin.businesses.operating-hours.index', $business)
                        ->with('success', 'Operating hours copied successfully.');
    }

    /**
     * Set standard business hours template.
     */
    public function setTemplate(Request $request, Business $business)
    {
        $validated = $request->validate([
            'template' => 'required|in:standard,extended,weekend_only,24_7'
        ]);

        $templates = [
            'standard' => [
                // Monday to Friday: 9 AM - 5 PM
                1 => ['open_time' => '09:00', 'close_time' => '17:00', 'is_closed' => false],
                2 => ['open_time' => '09:00', 'close_time' => '17:00', 'is_closed' => false],
                3 => ['open_time' => '09:00', 'close_time' => '17:00', 'is_closed' => false],
                4 => ['open_time' => '09:00', 'close_time' => '17:00', 'is_closed' => false],
                5 => ['open_time' => '09:00', 'close_time' => '17:00', 'is_closed' => false],
                // Weekend closed
                0 => ['is_closed' => true],
                6 => ['is_closed' => true],
            ],
            'extended' => [
                // Monday to Friday: 8 AM - 8 PM, Saturday: 9 AM - 5 PM
                1 => ['open_time' => '08:00', 'close_time' => '20:00', 'is_closed' => false],
                2 => ['open_time' => '08:00', 'close_time' => '20:00', 'is_closed' => false],
                3 => ['open_time' => '08:00', 'close_time' => '20:00', 'is_closed' => false],
                4 => ['open_time' => '08:00', 'close_time' => '20:00', 'is_closed' => false],
                5 => ['open_time' => '08:00', 'close_time' => '20:00', 'is_closed' => false],
                6 => ['open_time' => '09:00', 'close_time' => '17:00', 'is_closed' => false],
                0 => ['is_closed' => true],
            ],
            'weekend_only' => [
                // Weekend only
                6 => ['open_time' => '10:00', 'close_time' => '18:00', 'is_closed' => false],
                0 => ['open_time' => '10:00', 'close_time' => '18:00', 'is_closed' => false],
                // Weekdays closed
                1 => ['is_closed' => true],
                2 => ['is_closed' => true],
                3 => ['is_closed' => true],
                4 => ['is_closed' => true],
                5 => ['is_closed' => true],
            ],
            '24_7' => [
                // 24/7 operation
                0 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
                1 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
                2 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
                3 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
                4 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
                5 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
                6 => ['open_time' => '00:00', 'close_time' => '23:59', 'is_closed' => false],
            ]
        ];

        $template = $templates[$validated['template']];

        foreach ($template as $dayOfWeek => $hours) {
            BusinessOperatingHour::updateOrCreate(
                [
                    'business_id' => $business->id,
                    'day_of_week' => $dayOfWeek
                ],
                [
                    'is_closed' => $hours['is_closed'] ?? false,
                    'open_time' => $hours['open_time'] ?? null,
                    'close_time' => $hours['close_time'] ?? null,
                    'break_times' => null
                ]
            );
        }

        return redirect()->route('admin.businesses.operating-hours.index', $business)
                        ->with('success', 'Operating hours template applied successfully.');
    }
}

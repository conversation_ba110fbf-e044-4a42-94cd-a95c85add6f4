<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business;
use Illuminate\Http\Request;

class BusinessManagementController extends Controller
{
    // Note: Middleware is applied at the route level in routes/web.php

    /**
     * Display operating hours management with business selection.
     */
    public function operatingHours(Request $request)
    {
        $businessId = $request->get('business_id');
        
        // If business_id is provided, redirect to specific business operating hours
        if ($businessId) {
            $business = Business::findOrFail($businessId);
            return redirect()->route('admin.businesses.operating-hours.index', $business);
        }

        // Get all businesses for selection
        $businesses = Business::active()->get();

        // If only one business exists, redirect directly to it
        if ($businesses->count() === 1) {
            return redirect()->route('admin.businesses.operating-hours.index', $businesses->first());
        }

        return view('admin.businesses.management.operating-hours', compact('businesses'));
    }

    /**
     * Display holidays management with business selection.
     */
    public function holidays(Request $request)
    {
        $businessId = $request->get('business_id');
        
        // If business_id is provided, redirect to specific business holidays
        if ($businessId) {
            $business = Business::findOrFail($businessId);
            return redirect()->route('admin.businesses.holidays.index', $business);
        }

        // Get all businesses for selection
        $businesses = Business::active()->get();

        // If only one business exists, redirect directly to it
        if ($businesses->count() === 1) {
            return redirect()->route('admin.businesses.holidays.index', $businesses->first());
        }

        return view('admin.businesses.management.holidays', compact('businesses'));
    }

    /**
     * Display locations management with business selection.
     */
    public function locations(Request $request)
    {
        $businessId = $request->get('business_id');
        
        // If business_id is provided, redirect to specific business locations
        if ($businessId) {
            $business = Business::findOrFail($businessId);
            return redirect()->route('admin.businesses.locations.index', $business);
        }

        // Get all businesses for selection
        $businesses = Business::active()->get();

        // If only one business exists, redirect directly to it
        if ($businesses->count() === 1) {
            return redirect()->route('admin.businesses.locations.index', $businesses->first());
        }

        return view('admin.businesses.management.locations', compact('businesses'));
    }
}

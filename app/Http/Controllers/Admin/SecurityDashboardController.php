<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\SecurityMonitoringService;
use App\Services\ComplianceReportingService;
use App\Services\PermissionInheritanceService;
use App\Services\DataRetentionService;
use App\Models\SecurityAlert;
use App\Models\RoleAuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SecurityDashboardController extends Controller
{
    protected $securityMonitoring;
    protected $complianceReporting;
    protected $permissionInheritance;
    protected $dataRetention;

    public function __construct(
        SecurityMonitoringService $securityMonitoring,
        ComplianceReportingService $complianceReporting,
        PermissionInheritanceService $permissionInheritance,
        DataRetentionService $dataRetention
    ) {
        $this->securityMonitoring = $securityMonitoring;
        $this->complianceReporting = $complianceReporting;
        $this->permissionInheritance = $permissionInheritance;
        $this->dataRetention = $dataRetention;
    }

    /**
     * Display the security dashboard
     */
    public function index()
    {
        $dashboardData = $this->securityMonitoring->getSecurityDashboardData();
        $alertStatistics = SecurityAlert::getStatistics(24);
        $complianceScore = $this->complianceReporting->generateSecurityAuditReport(['format' => 'array'])['executive_summary']['compliance_score'];

        return view('admin.security.dashboard', compact(
            'dashboardData',
            'alertStatistics',
            'complianceScore'
        ));
    }

    /**
     * Display security alerts
     */
    public function alerts(Request $request)
    {
        $query = SecurityAlert::with('user', 'resolver')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('severity')) {
            $query->where('severity', $request->severity);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $alerts = $query->paginate(20);
        $statistics = SecurityAlert::getStatistics(24);

        return view('admin.security.alerts', compact('alerts', 'statistics'));
    }

    /**
     * Show specific security alert
     */
    public function showAlert(SecurityAlert $alert)
    {
        $alert->load('user', 'resolver');

        return view('admin.security.alert-details', compact('alert'));
    }

    /**
     * Resolve security alert
     */
    public function resolveAlert(Request $request, SecurityAlert $alert)
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        $alert->resolve($request->resolution_notes);

        return redirect()->back()->with('success', 'Alert resolved successfully.');
    }

    /**
     * Mark alert as false positive
     */
    public function markFalsePositive(Request $request, SecurityAlert $alert)
    {
        $request->validate([
            'resolution_notes' => 'nullable|string|max:1000',
        ]);

        $alert->markAsFalsePositive($request->resolution_notes);

        return redirect()->back()->with('success', 'Alert marked as false positive.');
    }

    /**
     * Display audit logs
     */
    public function auditLogs(Request $request)
    {
        $query = RoleAuditLog::with('user')
            ->orderBy('created_at', 'desc');

        // Apply filters
        if ($request->filled('action')) {
            $query->where('action', $request->action);
        }

        if ($request->filled('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        if ($request->filled('risk_level')) {
            $query->where('risk_level', $request->risk_level);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $logs = $query->paginate(25);
        $actions = RoleAuditLog::ACTIONS;
        $riskLevels = RoleAuditLog::RISK_LEVELS;

        return view('admin.security.audit-logs', compact('logs', 'actions', 'riskLevels'));
    }

    /**
     * Display compliance reports
     */
    public function complianceReports()
    {
        $report = $this->complianceReporting->generateSecurityAuditReport([
            'start_date' => now()->subDays(30),
            'end_date' => now(),
            'format' => 'array'
        ]);

        return view('admin.security.compliance-reports', compact('report'));
    }

    /**
     * Generate compliance report
     */
    public function generateComplianceReport(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:array,pdf,csv',
        ]);

        $report = $this->complianceReporting->generateSecurityAuditReport([
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'format' => $request->format,
        ]);

        if ($request->format === 'pdf') {
            return response()->download($report);
        } elseif ($request->format === 'csv') {
            return response()->download($report);
        }

        return response()->json($report);
    }

    /**
     * Display permission inheritance visualization
     */
    public function permissionInheritance()
    {
        $hierarchyData = $this->permissionInheritance->getRoleHierarchyVisualization();
        $conflictAnalysis = $this->permissionInheritance->analyzePermissionConflicts();

        return view('admin.security.permission-inheritance', compact(
            'hierarchyData',
            'conflictAnalysis'
        ));
    }

    /**
     * Get permission inheritance tree for specific permission
     */
    public function getPermissionTree(Request $request)
    {
        $request->validate([
            'permission' => 'required|string',
        ]);

        $tree = $this->permissionInheritance->getPermissionInheritanceTree($request->permission);

        return response()->json($tree);
    }

    /**
     * Display data retention status
     */
    public function dataRetention()
    {
        $retentionStatus = $this->dataRetention->getRetentionPolicyStatus();
        $availableArchives = $this->dataRetention->getAvailableArchives();

        return view('admin.security.data-retention', compact(
            'retentionStatus',
            'availableArchives'
        ));
    }

    /**
     * Execute data retention policies
     */
    public function executeRetentionPolicies()
    {
        $results = $this->dataRetention->executeRetentionPolicies();

        return response()->json([
            'success' => true,
            'message' => 'Data retention policies executed successfully.',
            'results' => $results,
        ]);
    }

    /**
     * Restore data from archive
     */
    public function restoreFromArchive(Request $request)
    {
        $request->validate([
            'data_type' => 'required|string',
            'archive_file' => 'required|string',
        ]);

        try {
            $result = $this->dataRetention->restoreFromArchive(
                $request->data_type,
                $request->archive_file
            );

            return response()->json([
                'success' => true,
                'message' => 'Data restored successfully.',
                'result' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to restore data: ' . $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Run security monitoring scan
     */
    public function runSecurityScan()
    {
        $alerts = $this->securityMonitoring->monitorSecurityEvents();

        return response()->json([
            'success' => true,
            'message' => 'Security scan completed.',
            'alerts_generated' => count($alerts),
            'alerts' => $alerts,
        ]);
    }

    /**
     * Get real-time security metrics
     */
    public function getSecurityMetrics()
    {
        $metrics = [
            'alerts_last_24h' => SecurityAlert::recent(24)->count(),
            'critical_alerts' => SecurityAlert::recent(24)->critical()->count(),
            'open_alerts' => SecurityAlert::open()->count(),
            'audit_activities_today' => RoleAuditLog::whereDate('created_at', today())->count(),
            'high_risk_activities' => RoleAuditLog::where('risk_level', '>=', 4)
                ->whereDate('created_at', today())->count(),
        ];

        return response()->json($metrics);
    }

    /**
     * Get security trends data
     */
    public function getSecurityTrends(Request $request)
    {
        $days = $request->get('days', 30);
        $startDate = now()->subDays($days);

        $trends = [
            'alerts_by_day' => SecurityAlert::where('created_at', '>=', $startDate)
                ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get(),
            'alerts_by_severity' => SecurityAlert::where('created_at', '>=', $startDate)
                ->selectRaw('severity, COUNT(*) as count')
                ->groupBy('severity')
                ->get(),
            'alerts_by_type' => SecurityAlert::where('created_at', '>=', $startDate)
                ->selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json($trends);
    }

    /**
     * Export security data
     */
    public function exportSecurityData(Request $request)
    {
        $request->validate([
            'type' => 'required|in:alerts,audit_logs,compliance_report',
            'format' => 'required|in:csv,json,pdf',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        $dateFrom = $request->date_from ? \Carbon\Carbon::parse($request->date_from) : now()->subDays(30);
        $dateTo = $request->date_to ? \Carbon\Carbon::parse($request->date_to) : now();

        switch ($request->type) {
            case 'alerts':
                return $this->exportAlerts($request->format, $dateFrom, $dateTo);
            case 'audit_logs':
                return $this->exportAuditLogs($request->format, $dateFrom, $dateTo);
            case 'compliance_report':
                return $this->exportComplianceReport($request->format, $dateFrom, $dateTo);
        }
    }

    /**
     * Export security alerts
     */
    private function exportAlerts(string $format, $dateFrom, $dateTo)
    {
        $alerts = SecurityAlert::with('user')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $filename = "security_alerts_{$dateFrom->format('Y_m_d')}_to_{$dateTo->format('Y_m_d')}.{$format}";

        if ($format === 'csv') {
            return $this->generateCSV($alerts, $filename, [
                'ID', 'Type', 'Severity', 'Status', 'User', 'Message', 'IP Address', 'Created At'
            ]);
        } elseif ($format === 'json') {
            return response()->json($alerts);
        }

        // PDF export would be implemented here
        return response()->json(['message' => 'PDF export not implemented yet']);
    }

    /**
     * Export audit logs
     */
    private function exportAuditLogs(string $format, $dateFrom, $dateTo)
    {
        $logs = RoleAuditLog::with('user')
            ->whereBetween('created_at', [$dateFrom, $dateTo])
            ->get();

        $filename = "audit_logs_{$dateFrom->format('Y_m_d')}_to_{$dateTo->format('Y_m_d')}.{$format}";

        if ($format === 'csv') {
            return $this->generateCSV($logs, $filename, [
                'ID', 'Action', 'User', 'Target', 'Risk Level', 'IP Address', 'Created At'
            ]);
        } elseif ($format === 'json') {
            return response()->json($logs);
        }

        return response()->json(['message' => 'PDF export not implemented yet']);
    }

    /**
     * Export compliance report
     */
    private function exportComplianceReport(string $format, $dateFrom, $dateTo)
    {
        $report = $this->complianceReporting->generateSecurityAuditReport([
            'start_date' => $dateFrom,
            'end_date' => $dateTo,
            'format' => $format,
        ]);

        if ($format === 'pdf' || $format === 'csv') {
            return response()->download($report);
        }

        return response()->json($report);
    }

    /**
     * Generate CSV file
     */
    private function generateCSV($data, string $filename, array $headers)
    {
        $csvData = [];
        $csvData[] = $headers;

        foreach ($data as $item) {
            $row = [];
            foreach ($headers as $header) {
                switch ($header) {
                    case 'ID':
                        $row[] = $item->id;
                        break;
                    case 'Type':
                        $row[] = $item->type_name ?? $item->type;
                        break;
                    case 'Severity':
                        $row[] = $item->severity_name ?? $item->severity;
                        break;
                    case 'Status':
                        $row[] = $item->status_name ?? $item->status;
                        break;
                    case 'Action':
                        $row[] = $item->action_name ?? $item->action;
                        break;
                    case 'User':
                        $row[] = $item->user->name ?? 'Unknown';
                        break;
                    case 'Target':
                        $row[] = $item->target_name ?? '';
                        break;
                    case 'Message':
                        $row[] = $item->message ?? '';
                        break;
                    case 'Risk Level':
                        $row[] = $item->risk_level_name ?? $item->risk_level;
                        break;
                    case 'IP Address':
                        $row[] = $item->ip_address ?? '';
                        break;
                    case 'Created At':
                        $row[] = $item->created_at->format('Y-m-d H:i:s');
                        break;
                }
            }
            $csvData[] = $row;
        }

        $output = fopen('php://temp', 'w');
        foreach ($csvData as $row) {
            fputcsv($output, $row);
        }
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);

        return response($csv)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }
}

<?php

namespace App\Http\Controllers\Owner;

use App\Http\Controllers\Owner\Controller;
use App\Models\Booking;
use App\Models\Service;
use App\Models\User;
use App\Services\BookingIntegrationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Facades\Activity;

class CheckInController extends Controller
{
    protected $integrationService;

    public function __construct(BookingIntegrationService $integrationService)
    {
        $this->integrationService = $integrationService;
    }

    /**
     * Display the check-in dashboard.
     */
    public function index(Request $request)
    {
        $business = $this->getUserBusiness();
        $date = $request->get('date', now()->format('Y-m-d'));
        $selectedDate = Carbon::parse($date);

        // Get today's bookings for the business
        $bookingsQuery = $business->bookings()
            ->with(['customer', 'services', 'business'])
            ->whereDate('start_datetime', $selectedDate)
            ->where('status', '!=', 'cancelled');

        // Apply filters
        if ($request->filled('service_id')) {
            $bookingsQuery->whereHas('services', function ($query) use ($request) {
                $query->where('services.id', $request->service_id);
            });
        }

        if ($request->filled('status')) {
            $status = $request->status;
            if ($status === 'waiting') {
                $bookingsQuery->whereNull('checked_in_at')->where('status', 'confirmed');
            } elseif ($status === 'checked_in') {
                $bookingsQuery->whereNotNull('checked_in_at')->whereNull('checked_out_at');
            } elseif ($status === 'completed') {
                $bookingsQuery->whereNotNull('checked_out_at');
            } elseif ($status === 'no_show') {
                $bookingsQuery->where('status', 'no_show');
            }
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $bookingsQuery->where(function ($query) use ($search) {
                $query->where('customer_name', 'like', "%{$search}%")
                      ->orWhere('customer_email', 'like', "%{$search}%")
                      ->orWhere('customer_phone', 'like', "%{$search}%")
                      ->orWhere('booking_number', 'like', "%{$search}%");
            });
        }

        $bookings = $bookingsQuery->orderBy('start_datetime')->get();

        // Group bookings by status
        $waitingToCheckIn = $bookings->filter(function ($booking) {
            return $booking->status === 'confirmed' && !$booking->checked_in_at;
        });

        $currentlyCheckedIn = $bookings->filter(function ($booking) {
            return $booking->checked_in_at && !$booking->checked_out_at;
        });

        $completedToday = $bookings->filter(function ($booking) {
            return $booking->checked_out_at;
        });

        $noShows = $bookings->where('status', 'no_show');

        // Statistics
        $stats = [
            'total' => $bookings->count(),
            'waiting' => $waitingToCheckIn->count(),
            'checked_in' => $currentlyCheckedIn->count(),
            'completed' => $completedToday->count(),
            'no_show' => $noShows->count(),
        ];

        // Get services for filter dropdown
        $services = $business->services()->active()->get();

        // Get next appointment
        $nextAppointment = $business->bookings()
            ->where('start_datetime', '>', now())
            ->where('status', 'confirmed')
            ->orderBy('start_datetime')
            ->first();

        // Get waiting room count (checked in but not started service)
        $waitingRoomCount = $currentlyCheckedIn->filter(function ($booking) {
            return $booking->start_datetime > now();
        })->count();

        return view('owner.check-in.index', compact(
            'bookings',
            'waitingToCheckIn',
            'currentlyCheckedIn',
            'completedToday',
            'noShows',
            'stats',
            'services',
            'selectedDate',
            'nextAppointment',
            'waitingRoomCount'
        ));
    }

    /**
     * Get check-in statistics for AJAX requests.
     */
    public function getStats(Request $request)
    {
        $business = $this->getUserBusiness();
        $date = $request->get('date', now()->format('Y-m-d'));
        $selectedDate = Carbon::parse($date);

        $bookings = $business->bookings()
            ->whereDate('start_datetime', $selectedDate)
            ->where('status', '!=', 'cancelled')
            ->get();

        $stats = [
            'total' => $bookings->count(),
            'waiting' => $bookings->where('status', 'confirmed')->whereNull('checked_in_at')->count(),
            'checked_in' => $bookings->whereNotNull('checked_in_at')->whereNull('checked_out_at')->count(),
            'completed' => $bookings->whereNotNull('checked_out_at')->count(),
            'no_show' => $bookings->where('status', 'no_show')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Check in a customer via AJAX.
     */
    public function checkIn(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $booking = $business->bookings()->findOrFail($id);

        if ($booking->checked_in_at) {
            return response()->json([
                'success' => false,
                'message' => 'Customer is already checked in.'
            ], 422);
        }

        if (!$booking->can_be_checked_in) {
            return response()->json([
                'success' => false,
                'message' => 'This booking cannot be checked in at this time.'
            ], 422);
        }

        $booking->checkIn();

        // Log the activity
        activity()
            ->performedOn($booking)
            ->withProperties([
                'action' => 'check_in',
                'checked_in_by' => auth()->id(),
                'checked_in_at' => now()
            ])
            ->log('Customer checked in');

        return response()->json([
            'success' => true,
            'message' => 'Customer checked in successfully!',
            'booking' => $booking->load(['customer', 'services']),
            'trigger_integration' => true,
            'integration_event' => 'check_in'
        ]);
    }

    /**
     * Check out a customer via AJAX.
     */
    public function checkOut(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $booking = $business->bookings()->findOrFail($id);

        if (!$booking->checked_in_at) {
            return response()->json([
                'success' => false,
                'message' => 'Customer must be checked in first.'
            ], 422);
        }

        if ($booking->checked_out_at) {
            return response()->json([
                'success' => false,
                'message' => 'Customer is already checked out.'
            ], 422);
        }

        $validated = $request->validate([
            'notes' => 'nullable|string|max:1000',
        ]);

        $oldStatus = $booking->status;

        $booking->update([
            'checked_out_at' => now(),
            'status' => 'completed',
            'internal_notes' => $validated['notes'] ?? $booking->internal_notes
        ]);

        // Handle integration actions for status change
        $integrationResult = $this->integrationService->handleBookingStatusChange($booking, $oldStatus, 'completed');

        // Log the activity
        activity()
            ->performedOn($booking)
            ->withProperties([
                'action' => 'check_out',
                'checked_out_by' => auth()->id(),
                'checked_out_at' => now(),
                'notes' => $validated['notes'] ?? null
            ])
            ->log('Customer checked out');

        return response()->json([
            'success' => true,
            'message' => 'Customer checked out successfully!',
            'booking' => $booking->load(['customer', 'services']),
            'trigger_integration' => true,
            'integration_event' => 'check_out',
            'integration_actions' => $integrationResult['actions'] ?? []
        ]);
    }

    /**
     * Mark a booking as no-show via AJAX.
     */
    public function markNoShow(Request $request, $id)
    {
        $business = $this->getUserBusiness();
        $booking = $business->bookings()->findOrFail($id);

        if ($booking->status === 'no_show') {
            return response()->json([
                'success' => false,
                'message' => 'Booking is already marked as no-show.'
            ], 422);
        }

        if ($booking->checked_in_at) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot mark as no-show - customer is already checked in.'
            ], 422);
        }

        $validated = $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        $booking->update([
            'status' => 'no_show',
            'internal_notes' => $validated['reason'] ?? $booking->internal_notes
        ]);

        // Log the activity
        activity()
            ->performedOn($booking)
            ->withProperties([
                'action' => 'mark_no_show',
                'marked_by' => auth()->id(),
                'reason' => $validated['reason'] ?? null
            ])
            ->log('Booking marked as no-show');

        return response()->json([
            'success' => true,
            'message' => 'Booking marked as no-show.',
            'booking' => $booking->load(['customer', 'services'])
        ]);
    }

    /**
     * Search for bookings.
     */
    public function search(Request $request)
    {
        $business = $this->getUserBusiness();
        $query = $request->get('q');

        if (empty($query)) {
            return response()->json([]);
        }

        $bookings = $business->bookings()
            ->with(['customer', 'services'])
            ->where(function ($queryBuilder) use ($query) {
                $queryBuilder->where('customer_name', 'like', "%{$query}%")
                           ->orWhere('customer_email', 'like', "%{$query}%")
                           ->orWhere('customer_phone', 'like', "%{$query}%")
                           ->orWhere('booking_number', 'like', "%{$query}%");
            })
            ->whereDate('start_datetime', '>=', now()->subDays(7))
            ->orderBy('start_datetime', 'desc')
            ->limit(10)
            ->get();

        return response()->json($bookings);
    }
}

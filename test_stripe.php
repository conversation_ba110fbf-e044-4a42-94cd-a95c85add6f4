<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';

// Bootstrap the application
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Stripe Connect Service...\n";

try {
    $service = $app->make('App\Services\StripeConnectService');
    echo "✅ StripeConnectService loaded successfully\n";
    
    // Test configuration
    $stripeKey = config('services.stripe.key');
    $stripeSecret = config('services.stripe.secret') ? 'Set' : 'Not Set';
    $stripeClientId = config('services.stripe.client_id') ? 'Set' : 'Not Set';
    
    echo "✅ Stripe Key: " . substr($stripeKey, 0, 20) . "...\n";
    echo "✅ Stripe Secret: $stripeSecret\n";
    echo "✅ Stripe Client ID: $stripeClientId\n";
    
} catch (Exception $e) {
    echo "❌ Error loading StripeConnectService: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\nTesting PaymentController...\n";

try {
    $controller = $app->make('App\Http\Controllers\Owner\PaymentController');
    echo "✅ PaymentController loaded successfully\n";
} catch (Exception $e) {
    echo "❌ Error loading PaymentController: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\nTesting Business and User setup...\n";

try {
    // Check if we have any businesses
    $businessCount = \App\Models\Business::count();
    echo "✅ Total businesses in database: $businessCount\n";

    // Check if we have any users
    $userCount = \App\Models\User::count();
    echo "✅ Total users in database: $userCount\n";

    // Check if we have any business owners
    $ownerCount = \App\Models\User::whereHas('roles', function($q) {
        $q->where('name', 'Business Owner');
    })->count();
    echo "✅ Total business owners: $ownerCount\n";

} catch (Exception $e) {
    echo "❌ Error checking database: " . $e->getMessage() . "\n";
}

echo "\nTest completed.\n";
